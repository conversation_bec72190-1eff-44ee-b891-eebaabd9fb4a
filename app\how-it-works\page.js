'use client';

import { useState } from 'react';
import Image from "next/image";
import Header from '../components/Header';
import Footer from '../components/Footer';

export default function HowItWorksPage() {
  const [activeSection, setActiveSection] = useState('logistics-network');

  const sections = [
    {
      id: 'logistics-network',
      title: 'Logistics Network',
      subtitle: 'Transform supply chains with North America\'s largest flexible warehousing network of more than 700+ operators.',
      image: 'https://images.unsplash.com/photo-*************-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      features: [
        'Access to 700+ warehouse operators',
        'Nationwide coverage across US and Canada',
        'Vetted and qualified partners',
        'Scalable capacity on demand'
      ]
    },
    {
      id: 'technology-platform',
      title: 'Technology Platform',
      subtitle: 'Integrate once to manage distribution and fulfillment orders, inventory and operations across 700+ warehouse operators.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      features: [
        'Single API integration',
        'Real-time inventory management',
        'Order management system',
        'Performance analytics dashboard'
      ]
    },
    {
      id: 'data-intelligence',
      title: 'Data Intelligence',
      subtitle: 'Make data-informed network investments with proprietary Flexe insights, supply chain modeling and total cost analysis.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      features: [
        'Proprietary market insights',
        'Supply chain modeling',
        'Total cost analysis',
        'Performance benchmarking'
      ]
    },
    {
      id: 'operations-team',
      title: 'Operations Team',
      subtitle: 'Scale and optimize across the network through a single, dedicated operations team. Ensure operational excellence through enhanced quality, consistent performance and resource efficiency.',
      image: 'https://images.unsplash.com/photo-*************-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      features: [
        'Dedicated account management',
        '24/7 operational support',
        'Quality assurance programs',
        'Performance optimization'
      ]
    }
  ];

  const currentSection = sections.find(section => section.id === activeSection);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="how-it-works" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern warehouse operations and technology"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-*************-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
            <i className="fas fa-cogs text-[#E6B24B] mr-3"></i>
            <span className="text-white font-semibold">How It Works</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
            How Flexible Warehousing
            <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
              Infrastructure works
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Expand fixed networks with Flexible Warehousing Infrastructure through our comprehensive platform and network.
          </p>
        </div>
      </section>

      {/* Interactive Sections */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Navigation */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                  activeSection === section.id
                    ? 'bg-gradient-to-br from-[#E6B24B]/20 to-[#D4A843]/20 border-2 border-[#E6B24B]/50'
                    : 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${
                  activeSection === section.id ? 'bg-[#E6B24B]' : 'bg-[#E6B24B]/20'
                }`}>
                  <i className={`fas ${
                    section.id === 'logistics-network' ? 'fa-network-wired' :
                    section.id === 'technology-platform' ? 'fa-laptop-code' :
                    section.id === 'data-intelligence' ? 'fa-chart-line' :
                    'fa-users'
                  } ${activeSection === section.id ? 'text-black' : 'text-[#E6B24B]'} text-xl`}></i>
                </div>
                <h3 className={`font-bold mb-2 ${
                  activeSection === section.id ? 'text-[#E6B24B]' : 'text-white'
                }`}>
                  {section.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {section.subtitle.substring(0, 80)}...
                </p>
              </button>
            ))}
          </div>

          {/* Active Section Content */}
          {currentSection && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-4xl font-bold text-white mb-6">
                  {currentSection.title}
                </h2>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  {currentSection.subtitle}
                </p>

                <div className="space-y-4 mb-8">
                  {currentSection.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full flex-shrink-0"></div>
                      <span className="text-gray-300">{feature}</span>
                    </div>
                  ))}
                </div>

                <button className="btn-primary btn-lg">
                  Learn More
                </button>
              </div>

              <div className="relative">
                <img
                  alt={currentSection.title}
                  className="w-full h-96 object-cover rounded-2xl"
                  src={currentSection.image}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-2xl"></div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Process Flow Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">
              Simple Integration Process
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get started with Flexible Warehousing Infrastructure in just a few steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">1</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Connect</h3>
              <p className="text-gray-300 leading-relaxed">
                Single API integration connects you to our entire network of 700+ warehouse operators across North America.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">2</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Configure</h3>
              <p className="text-gray-300 leading-relaxed">
                Our operations team works with you to configure the optimal network setup for your specific requirements.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">3</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Scale</h3>
              <p className="text-gray-300 leading-relaxed">
                Scale your operations dynamically with real-time visibility and control across your entire logistics network.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">
              Why Choose Flexible Warehousing Infrastructure
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-rocket text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Rapid Deployment</h3>
              <p className="text-body leading-relaxed">
                Deploy new warehouse capacity in days, not months. No long-term leases or capital investments required.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-expand-arrows-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Flexible Scaling</h3>
              <p className="text-body leading-relaxed">
                Scale up or down based on demand. Pay only for the capacity you use when you use it.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-shield-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Enterprise Grade</h3>
              <p className="text-body leading-relaxed">
                Enterprise-grade security, compliance, and performance standards across our entire network.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Data-Driven Insights</h3>
              <p className="text-body leading-relaxed">
                Make informed decisions with real-time analytics and performance insights across your network.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-headset text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">24/7 Support</h3>
              <p className="text-body leading-relaxed">
                Dedicated operations team provides round-the-clock support and optimization recommendations.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-globe text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Network Coverage</h3>
              <p className="text-body leading-relaxed">
                Access to strategic locations across North America for optimal supply chain positioning.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Supply Chain?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Discover how Flexible Warehousing Infrastructure can optimize your logistics operations and drive growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button className="btn-primary btn-xl">
              Get Started Today
            </button>
            <button className="btn-secondary btn-xl">
              Schedule Demo
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
