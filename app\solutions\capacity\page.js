import Header from '../../components/Header';
import Footer from '../../components/Footer';

export default function CapacityPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="solutions" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Large warehouse capacity and storage"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-expand-arrows-alt text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Capacity Solutions</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              Flexible Warehouse
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                Capacity On-Demand
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Scale your warehouse capacity up or down instantly with our flexible network of premium facilities across North America.
            </p>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Capacity Benefits
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-bolt text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Instant Scalability</h3>
              <p className="text-gray-300 leading-relaxed">
                Add warehouse capacity within days, not months. Scale up for peak seasons or new market expansion.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-dollar-sign text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Cost Efficiency</h3>
              <p className="text-gray-300 leading-relaxed">
                Pay only for the space you use, when you use it. No long-term leases or capital investments required.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-map-marked-alt text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Strategic Locations</h3>
              <p className="text-gray-300 leading-relaxed">
                Access premium warehouse locations in key markets without the overhead of facility management.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-cogs text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Operational Flexibility</h3>
              <p className="text-gray-300 leading-relaxed">
                Adapt to changing business needs with flexible terms and operational configurations.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-shield-alt text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Risk Mitigation</h3>
              <p className="text-gray-300 leading-relaxed">
                Reduce operational risk with proven facilities and experienced warehouse operators.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Performance Monitoring</h3>
              <p className="text-gray-300 leading-relaxed">
                Real-time visibility into capacity utilization, costs, and operational performance metrics.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Capacity Use Cases
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-calendar-alt text-black text-lg"></i>
                </div>
                <h3 className="text-xl font-bold text-white">Seasonal Peaks</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Handle holiday rushes, back-to-school seasons, and other peak periods without long-term commitments. Scale capacity up during busy times and down during slower periods.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-rocket text-black text-lg"></i>
                </div>
                <h3 className="text-xl font-bold text-white">Market Expansion</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Test new markets with minimal risk. Establish warehouse presence in new regions quickly and cost-effectively before making long-term facility commitments.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-exclamation-triangle text-black text-lg"></i>
                </div>
                <h3 className="text-xl font-bold text-white">Overflow Capacity</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Handle unexpected inventory spikes or facility disruptions. Quickly access additional warehouse space when your primary facilities reach capacity limits.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-boxes text-black text-lg"></i>
                </div>
                <h3 className="text-xl font-bold text-white">Product Launches</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Support new product launches with dedicated warehouse space. Ensure adequate inventory positioning and fulfillment capacity for successful market introductions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Need Flexible Warehouse Capacity?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's discuss how our on-demand capacity solutions can support your business growth and operational flexibility.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
              Get Started
            </a>
            <a href="/solutions" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              View All Solutions
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
