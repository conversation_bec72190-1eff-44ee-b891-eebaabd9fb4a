'use client';

import { useState } from 'react';
import Image from "next/image";
import { resourcesData } from '../data/articles';
import Header from '../components/Header';
import Footer from '../components/Footer';

export default function ResourcesPage() {
  const [activeFilter, setActiveFilter] = useState('all');

  const resources = resourcesData;

  const filteredResources = activeFilter === 'all'
    ? resources
    : resources.filter(resource => resource.category === activeFilter);

  const featuredResources = resources.filter(resource => resource.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="resources" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern library and knowledge resources"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
            <i className="fas fa-book-open text-[#E6B24B] mr-3"></i>
            <span className="text-white font-semibold">Resources</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
            Supply Chain
            <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
              Knowledge Hub
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore our comprehensive collection of insights, research, and best practices to optimize your supply chain operations.
          </p>
        </div>
      </section>

      {/* Featured Resources */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Section Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-star text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Featured Content</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Featured
              <span className="block text-[#4caf50]">Insights</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our most popular and impactful resources for supply chain professionals
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {featuredResources.map((resource) => (
              <div key={resource.id} className="card-professional card-interactive group overflow-hidden">
                <div className="relative overflow-hidden p-0">
                  <img
                    alt={resource.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={resource.image}
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                      {resource.type}
                    </span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="p-6">
                  <h3 className="heading-tertiary text-xl mb-3 group-hover:text-accent transition-colors duration-300">
                    {resource.title}
                  </h3>
                  <p className="text-body mb-4 leading-relaxed">
                    {resource.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-body-small">{resource.readTime}</span>
                    <a href={`/resources/articles/${resource.slug}`} className="text-accent font-semibold hover:text-white transition-colors duration-300">
                      Read More <i className="fas fa-arrow-right ml-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* All Resources Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              All
              <span className="block text-[#E6B24B]">Resources</span>
            </h2>

            {/* Filter Buttons */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeFilter === 'all'
                    ? 'btn-primary'
                    : 'btn-tertiary'
                }`}
              >
                All Resources
              </button>
              <button
                onClick={() => setActiveFilter('case-study')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeFilter === 'case-study'
                    ? 'btn-primary'
                    : 'btn-tertiary'
                }`}
              >
                Case Studies
              </button>
              <button
                onClick={() => setActiveFilter('insights')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeFilter === 'insights'
                    ? 'btn-primary'
                    : 'btn-tertiary'
                }`}
              >
                Articles
              </button>
              <button
                onClick={() => setActiveFilter('research')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeFilter === 'research'
                    ? 'btn-primary'
                    : 'btn-tertiary'
                }`}
              >
                White Papers
              </button>
              <button
                onClick={() => setActiveFilter('education')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeFilter === 'education'
                    ? 'btn-primary'
                    : 'btn-tertiary'
                }`}
              >
                Webinars
              </button>
            </div>
          </div>

          {/* Resources Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredResources.map((resource) => (
              <div key={resource.id} className="card-professional card-interactive group overflow-hidden">
                <div className="relative overflow-hidden p-0">
                  <img
                    alt={resource.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={resource.image}
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                      {resource.type}
                    </span>
                  </div>
                  {resource.featured && (
                    <div className="absolute top-4 right-4">
                      <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="heading-tertiary text-lg mb-3 group-hover:text-accent transition-colors duration-300">
                    {resource.title}
                  </h3>
                  <p className="text-body mb-4 leading-relaxed text-sm">
                    {resource.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-body-small">{resource.readTime}</span>
                    <a href={`/resources/articles/${resource.slug}`} className="text-accent font-semibold hover:text-white transition-colors duration-300">
                      Read More <i className="fas fa-arrow-right ml-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="card-professional card-elevated p-16">
            <h2 className="heading-secondary mb-6">
              Stay Updated with
              <span className="block text-success">Supply Chain Insights</span>
            </h2>
            <p className="text-body-large mb-8 max-w-3xl mx-auto">
              Subscribe to our newsletter for the latest trends, insights, and best practices in supply chain management.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="form-input flex-1"
              />
              <button className="btn-primary btn-lg whitespace-nowrap">
                Subscribe
              </button>
            </div>

            <p className="text-body-small mt-4">
              No spam, unsubscribe at any time. We respect your privacy.
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
