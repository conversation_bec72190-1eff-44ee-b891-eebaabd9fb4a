'use client';

import { useEffect } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';

export default function Home() {
  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in-view');
          // Only animate once
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe all elements with scroll-animate class
    const animateElements = document.querySelectorAll('.scroll-animate');
    animateElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);
  return (
    <>
      <div style={{ backgroundColor: '#e3e8ef', color: '#0a1e3a' }}>
        <Header currentPage="home" />

        {/* ENHANCED HERO SECTION - IMPROVED CLARITY & READABILITY */}
        <section className="relative overflow-hidden bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
          {/* Simplified Background Pattern for Better Focus */}
          <div className="absolute inset-0">
            {/* Clean Gradient Overlay for Better Text Contrast */}
            <div className="absolute inset-0 bg-gradient-to-t from-[#0a1e3a]/80 via-[#0a1e3a]/40 to-transparent"></div>

            {/* Minimal Decorative Elements */}
            <div className="absolute top-32 right-32 w-24 h-24 border border-white/10 rounded-full"></div>
            <div className="absolute bottom-32 left-32 w-16 h-16 border border-white/10 rounded-full"></div>
          </div>

          <div className="max-w-7xl mx-auto px-8 lg:px-12 pt-32 pb-40 relative z-10">
            <div className="text-center max-w-6xl mx-auto">

              {/* Improved Main Heading - Better Typography Hierarchy */}
              <h1 className="font-bold leading-[1.1] mb-12">
                <span className="block text-white text-5xl lg:text-6xl xl:text-7xl mb-2">Warehouse</span>
                <span className="block text-white text-5xl lg:text-6xl xl:text-7xl mb-2">management</span>
                <span className="block text-white text-5xl lg:text-6xl xl:text-7xl">software.</span>
              </h1>

              {/* Improved Subtitle - Better Contrast & Spacing */}
              <div className="mb-16">
                <p className="text-white/90 text-xl lg:text-2xl leading-relaxed max-w-4xl mx-auto font-medium">
                  World's simplest and most efficient web-based multi-channel order fulfillment and warehouse management software (WMS).
                </p>
              </div>

              {/* Enhanced CTA Buttons - Better Visual Definition */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-20">
                <button className="bg-white text-[#0a1e3a] hover:bg-gray-100 font-bold text-lg rounded-full px-12 py-4 flex items-center justify-center space-x-3 shadow-2xl transition-all duration-300 hover:scale-105 hover:shadow-3xl">
                  <span>GET STARTED</span>
                  <i className="fas fa-arrow-right"></i>
                </button>

                <button className="border-2 border-white text-white hover:bg-white hover:text-[#0a1e3a] font-semibold text-lg rounded-full px-12 py-4 flex items-center justify-center space-x-3 transition-all duration-300 hover:scale-105">
                  <span>REQUEST DEMO</span>
                  <i className="fas fa-play"></i>
                </button>
              </div>

              {/* Improved Stats Section - Better Visual Hierarchy */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">

                {/* Stat 1 - Enhanced Readability */}
                <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 group border border-white/20">
                  <div className="text-5xl font-bold text-[#0a1e3a] mb-3">99.9%</div>
                  <div className="text-[#0a1e3a] text-lg font-semibold mb-1">System Uptime</div>
                  <div className="text-[#0a1e3a]/70 text-sm">Reliable & Secure</div>
                </div>

                {/* Stat 2 - Enhanced Readability */}
                <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 group border border-white/20">
                  <div className="text-5xl font-bold text-[#2a5a3a] mb-3">50K+</div>
                  <div className="text-[#0a1e3a] text-lg font-semibold mb-1">Orders Daily</div>
                  <div className="text-[#0a1e3a]/70 text-sm">High Performance</div>
                </div>

                {/* Stat 3 - Enhanced Readability */}
                <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 group border border-white/20">
                  <div className="text-5xl font-bold text-[#0a2e2a] mb-3">500+</div>
                  <div className="text-[#0a1e3a] text-lg font-semibold mb-1">Enterprise Clients</div>
                  <div className="text-[#0a1e3a]/70 text-sm">Trusted Worldwide</div>
                </div>

              </div>

            </div>
          </div>
        </section>

        {/* IMPROVED INDUSTRIES SECTION - ENHANCED CLARITY */}
        <section className="bg-white max-w-7xl mx-auto px-8 lg:px-12 py-24 shadow-xl">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16">

            {/* Left Side - Improved Industry Grid */}
            <div className="lg:col-span-7">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">

                {/* Food & Beverage - Enhanced Readability */}
                <div className="group bg-white border-2 border-gray-100 hover:border-[#2a5a3a] p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-6 h-6 rounded-full bg-[#2a5a3a] shadow-md"></div>
                    <h3 className="text-lg font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Food & Beverage</h3>
                  </div>
                  <p className="text-[#0a1e3a]/80 leading-relaxed">
                    Specialized solutions for food safety, temperature control, and compliance tracking in food and beverage warehouses.
                  </p>
                </div>

                {/* Home Improvement - Enhanced Readability */}
                <div className="group bg-white border-2 border-gray-100 hover:border-[#0a2e2a] p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-6 h-6 rounded-full bg-[#0a2e2a] shadow-md"></div>
                    <h3 className="text-lg font-bold text-[#0a1e3a] group-hover:text-[#0a2e2a] transition-colors duration-300">Home Improvement</h3>
                  </div>
                  <p className="text-[#0a1e3a]/80 leading-relaxed">
                    Manage large inventory items, seasonal products, and complex SKU variations with ease and precision.
                  </p>
                </div>

                {/* Pharmaceutical - Enhanced Readability */}
                <div className="group bg-white border-2 border-gray-100 hover:border-[#2a5a3a] p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-6 h-6 rounded-full bg-[#2a5a3a] shadow-md"></div>
                    <h3 className="text-lg font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Pharmaceutical</h3>
                  </div>
                  <p className="text-[#0a1e3a]/80 leading-relaxed">
                    Ensure regulatory compliance with batch tracking, expiration management, and controlled environment monitoring.
                  </p>
                </div>

                {/* Internet Retailers - Enhanced Readability */}
                <div className="group bg-white border-2 border-gray-100 hover:border-[#0a2e2a] p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-6 h-6 rounded-full bg-[#0a2e2a] shadow-md"></div>
                    <h3 className="text-lg font-bold text-[#0a1e3a] group-hover:text-[#0a2e2a] transition-colors duration-300">Internet Retailers</h3>
                  </div>
                  <p className="text-[#0a1e3a]/80 leading-relaxed">
                    Scale your e-commerce operations with multi-channel integration and automated order fulfillment.
                  </p>
                </div>

              </div>
            </div>

            {/* Right Side - Improved Content Structure */}
            <div className="lg:col-span-5 flex flex-col justify-center">
              <div className="space-y-8">
                <div>
                  <h2 className="text-4xl lg:text-5xl font-bold text-[#0a1e3a] mb-6 leading-tight">
                    Industries We Service
                  </h2>
                  <p className="text-lg text-[#0a1e3a]/80 leading-relaxed">
                    Our paperless WMS (Warehouse Management System) is trusted by leading businesses across the globe with industry-specific features and compliance tools.
                  </p>
                </div>

                <div>
                  <button className="bg-[#0a1e3a] hover:bg-[#2a5a3a] text-white font-semibold text-lg rounded-full px-10 py-4 shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    VIEW MORE INDUSTRIES
                  </button>
                </div>
              </div>
            </div>

          </div>
        </section>

        {/* IMPROVED INVENTORY FLOW SECTION - ENHANCED CLARITY */}
        <section className="bg-gray-50 max-w-7xl mx-auto px-8 lg:px-12 py-24">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">

            {/* Left Side - Improved Content Structure */}
            <div className="lg:col-span-6">
              <div className="space-y-8">
                <div>
                  <h2 className="text-4xl lg:text-5xl font-bold text-[#0a1e3a] mb-8 leading-tight">
                    Inventory Flow Championed
                  </h2>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start space-x-6 p-6 bg-white rounded-2xl shadow-lg border-l-4 border-[#2a5a3a]">
                    <div className="w-8 h-8 rounded-full bg-[#2a5a3a] flex items-center justify-center flex-shrink-0 mt-1">
                      <i className="fas fa-warehouse text-white text-sm"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#0a1e3a] mb-2">Specialized Storage Solutions</h4>
                      <p className="text-[#0a1e3a]/80 leading-relaxed">
                        Store products safely with specialty storage options like cold storage, hazardous material, and high value inventory management.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-6 p-6 bg-white rounded-2xl shadow-lg border-l-4 border-[#0a2e2a]">
                    <div className="w-8 h-8 rounded-full bg-[#0a2e2a] flex items-center justify-center flex-shrink-0 mt-1">
                      <i className="fas fa-sync-alt text-white text-sm"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#0a1e3a] mb-2">Automated Replenishment</h4>
                      <p className="text-[#0a1e3a]/80 leading-relaxed">
                        Periodic stock take and automated replenishments enable you to monitor inventory levels and eliminate stock out events.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-6 p-6 bg-white rounded-2xl shadow-lg border-l-4 border-[#2a5a3a]">
                    <div className="w-8 h-8 rounded-full bg-[#2a5a3a] flex items-center justify-center flex-shrink-0 mt-1">
                      <i className="fas fa-clipboard-check text-white text-sm"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#0a1e3a] mb-2">100% Accurate Audits</h4>
                      <p className="text-[#0a1e3a]/80 leading-relaxed">
                        Whether you track your inventory by batch, lot or even serial number, always get 100% accurate audits.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-4">
                  <button className="bg-[#0a1e3a] hover:bg-[#2a5a3a] text-white font-semibold text-lg rounded-full px-10 py-4 shadow-xl transition-all duration-300 hover:scale-105">
                    GET STARTED
                  </button>
                  <button className="border-2 border-[#0a1e3a] text-[#0a1e3a] hover:bg-[#0a1e3a] hover:text-white font-semibold text-lg rounded-full px-10 py-4 transition-all duration-300 hover:scale-105">
                    TRY IT FREE
                  </button>
                </div>
              </div>
            </div>

            {/* Right Side - Improved Dashboard Design */}
            <div className="lg:col-span-6">
              <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">

                {/* Dashboard Header */}
                <div className="flex items-center justify-between mb-8">
                  <h3 className="text-2xl font-bold text-[#0a1e3a]">Analytics Overview</h3>
                  <div className="w-3 h-3 bg-[#2a5a3a] rounded-full animate-pulse"></div>
                </div>

                {/* Metrics Cards */}
                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="bg-gradient-to-br from-[#2a5a3a]/10 to-[#2a5a3a]/5 p-6 rounded-2xl border border-[#2a5a3a]/20">
                    <div className="text-sm font-semibold text-[#2a5a3a] mb-2">Growth Rate</div>
                    <div className="text-3xl font-bold text-[#0a1e3a] mb-1">+ 12%</div>
                    <div className="text-xs text-[#0a1e3a]/60">vs last month</div>
                  </div>

                  <div className="bg-gradient-to-br from-[#0a2e2a]/10 to-[#0a2e2a]/5 p-6 rounded-2xl border border-[#0a2e2a]/20">
                    <div className="text-sm font-semibold text-[#0a2e2a] mb-2">Efficiency</div>
                    <div className="text-3xl font-bold text-[#0a1e3a] mb-1">+ 4.8%</div>
                    <div className="text-xs text-[#0a1e3a]/60">improvement</div>
                  </div>
                </div>

                {/* Main Metrics */}
                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#0a1e3a] mb-2">$450m</div>
                    <div className="text-sm font-semibold text-[#0a1e3a]/70">Total Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#0a1e3a] mb-2">8,901</div>
                    <div className="text-sm font-semibold text-[#0a1e3a]/70">Orders Processed</div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="bg-gradient-to-r from-[#0a1e3a] to-[#0a2e2a] rounded-2xl p-6 flex items-center justify-between">
                  <div className="text-white">
                    <div className="font-semibold mb-1">2024 Report</div>
                    <div className="text-sm text-white/80">Detailed Analytics</div>
                  </div>
                  <button className="bg-white/20 hover:bg-white/30 rounded-xl w-12 h-12 flex items-center justify-center text-white transition-all duration-300 hover:scale-110">
                    <i className="fas fa-arrow-right"></i>
                  </button>
                </div>

              </div>
            </div>

          </div>
        </section>

        {/* IMPROVED COMPANY LOGOS SECTION - ENHANCED CLARITY */}
        <section className="bg-white max-w-7xl mx-auto px-8 lg:px-12 py-20">
          <div className="text-center mb-16">
            <h3 className="text-2xl font-bold text-[#0a1e3a] mb-4">Trusted by Industry Leaders</h3>
            <p className="text-[#0a1e3a]/70 text-lg">Join thousands of companies worldwide who trust our platform</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 items-center">
            <div className="flex justify-center p-6 bg-gray-50 rounded-2xl hover:bg-white hover:shadow-lg transition-all duration-300 border border-gray-100">
              <img alt="FedEx Logistics company logo" className="h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9a5510d9-5102-4789-77bf-391b5a7a6a8c.jpg" width="144"/>
            </div>
            <div className="flex justify-center p-6 bg-gray-50 rounded-2xl hover:bg-white hover:shadow-lg transition-all duration-300 border border-gray-100">
              <img alt="Crane Worldwide Logistics company logo" className="h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0f431638-d78a-4551-cd41-7644bfec8fae.jpg" width="144"/>
            </div>
            <div className="flex justify-center p-6 bg-gray-50 rounded-2xl hover:bg-white hover:shadow-lg transition-all duration-300 border border-gray-100">
              <img alt="Bollore Logistics company logo" className="h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4819675f-e061-4a06-2144-05b136d669e9.jpg" width="144"/>
            </div>
            <div className="flex justify-center p-6 bg-gray-50 rounded-2xl hover:bg-white hover:shadow-lg transition-all duration-300 border border-gray-100">
              <img alt="CEVA Logistics company logo" className="h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c734b295-1592-409b-e4a1-f0fbf795847a.jpg" width="144"/>
            </div>
            <div className="flex justify-center p-6 bg-gray-50 rounded-2xl hover:bg-white hover:shadow-lg transition-all duration-300 border border-gray-100">
              <img alt="HAVI company logo" className="h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bf5a67af-3d7f-42dc-2374-c55cdec36f40.jpg" width="144"/>
            </div>
          </div>
        </section>

        {/* IMPROVED MOBILE APP SHOWCASE - ENHANCED CLARITY */}
        <section className="bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] max-w-7xl mx-auto px-8 lg:px-12 py-24 rounded-3xl shadow-2xl">

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

            {/* Left Side - Simplified Mobile Mockup */}
            <div className="relative max-w-sm mx-auto lg:mx-0">

              {/* Mobile Screen Container */}
              <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-4 shadow-2xl border border-white/20">
                <img alt="Mobile phone showing warehouse management app interface" className="rounded-2xl w-full shadow-xl" height="560" src="https://storage.googleapis.com/a1aa/image/1347993e-5ce4-4703-cd22-1a5752fe0d69.jpg" width="280"/>
              </div>

              {/* Feature Badges */}
              <div className="absolute -top-4 -right-4 bg-white text-[#0a1e3a] px-4 py-2 rounded-full shadow-xl font-semibold text-sm">
                Real-time Updates
              </div>

              <div className="absolute -bottom-4 -left-4 bg-white text-[#0a1e3a] px-4 py-2 rounded-full shadow-xl font-semibold text-sm">
                Mobile Ready
              </div>

            </div>

            {/* Right Side - Improved Content Structure */}
            <div className="text-white space-y-8">
              <div>
                <h3 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Complete eCommerce Solutions for Asia Pacific
                </h3>

                <p className="text-xl text-white/90 leading-relaxed mb-8">
                  From small businesses to enterprise operations, we provide scalable warehouse management solutions tailored to your specific needs.
                </p>
              </div>

              {/* Feature List */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-check text-[#0a1e3a] text-sm"></i>
                  </div>
                  <span className="text-lg text-white/90">Multi-channel order fulfillment</span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-check text-[#0a1e3a] text-sm"></i>
                  </div>
                  <span className="text-lg text-white/90">Real-time inventory tracking</span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-check text-[#0a1e3a] text-sm"></i>
                  </div>
                  <span className="text-lg text-white/90">Automated workflow management</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 pt-4">
                <button className="bg-white text-[#0a1e3a] hover:bg-gray-100 font-bold text-lg rounded-full px-10 py-4 shadow-2xl transition-all duration-300 hover:scale-105">
                  GET STARTED TODAY
                </button>
                <button className="border-2 border-white text-white hover:bg-white hover:text-[#0a1e3a] font-semibold text-lg rounded-full px-10 py-4 transition-all duration-300 hover:scale-105">
                  SCHEDULE DEMO
                </button>
              </div>
            </div>

          </div>

        </section>

        {/* IMPROVED DISCOVER SECTION - ENHANCED CLARITY */}
        <section className="bg-white max-w-7xl mx-auto px-8 lg:px-12 py-24">

          <div className="text-center mb-20">
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0a1e3a] mb-6">
              Discover Who We Are
            </h2>
            <p className="text-xl text-[#0a1e3a]/70 max-w-3xl mx-auto">
              Learn more about our comprehensive solutions and see how industry leaders trust our platform
            </p>
          </div>

          {/* Enhanced Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-6xl mx-auto mb-24">

            {/* Features */}
            <div className="bg-gray-50 p-8 rounded-3xl border border-gray-100 hover:border-[#2a5a3a] hover:shadow-xl transition-all duration-300 group">
              <div className="mx-auto mb-8 w-20 h-20 rounded-2xl bg-[#0a1e3a] flex items-center justify-center text-white text-3xl shadow-lg group-hover:bg-[#2a5a3a] transition-all duration-300">
                <i className="fas fa-cogs"></i>
              </div>
              <h4 className="text-2xl font-bold text-[#0a1e3a] mb-4 group-hover:text-[#2a5a3a] transition-colors duration-300">Advanced Features</h4>
              <p className="text-[#0a1e3a]/80 leading-relaxed text-lg">
                Ensure maximum output from your warehouse to achieve rising success on both B2B and B2C fronts with Flexe's smart & intuitive features.
              </p>
            </div>

            {/* Industry Solutions */}
            <div className="bg-gray-50 p-8 rounded-3xl border border-gray-100 hover:border-[#0a2e2a] hover:shadow-xl transition-all duration-300 group">
              <div className="mx-auto mb-8 w-20 h-20 rounded-2xl bg-[#0a1e3a] flex items-center justify-center text-white text-3xl shadow-lg group-hover:bg-[#0a2e2a] transition-all duration-300">
                <i className="fas fa-industry"></i>
              </div>
              <h4 className="text-2xl font-bold text-[#0a1e3a] mb-4 group-hover:text-[#0a2e2a] transition-colors duration-300">Industry Solutions</h4>
              <p className="text-[#0a1e3a]/80 leading-relaxed text-lg">
                Discover how you can start eCommerce fulfillment, streamline warehouse operations, manage logistics in-house, or solve your logistics challenges.
              </p>
            </div>

            {/* Case Studies */}
            <div className="bg-gray-50 p-8 rounded-3xl border border-gray-100 hover:border-[#2a5a3a] hover:shadow-xl transition-all duration-300 group">
              <div className="mx-auto mb-8 w-20 h-20 rounded-2xl bg-[#0a1e3a] flex items-center justify-center text-white text-3xl shadow-lg group-hover:bg-[#2a5a3a] transition-all duration-300">
                <i className="fas fa-chart-line"></i>
              </div>
              <h4 className="text-2xl font-bold text-[#0a1e3a] mb-4 group-hover:text-[#2a5a3a] transition-colors duration-300">Success Stories</h4>
              <p className="text-[#0a1e3a]/80 leading-relaxed text-lg">
                Find out how multiple global players such as Nestle, Luxasia, Zilingo & more have defined their transformational journeys with Flexe.
              </p>
            </div>

          </div>

          {/* Enhanced Testimonial Section */}
          <div className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-12 shadow-xl border border-gray-100 max-w-4xl mx-auto">

            <div className="text-center">
              <div className="w-24 h-24 rounded-full bg-[#0a1e3a] mx-auto mb-8 flex items-center justify-center text-white text-4xl shadow-xl">
                <i className="fas fa-quote-left"></i>
              </div>

              <blockquote className="text-2xl lg:text-3xl text-[#0a1e3a] mb-8 leading-relaxed font-semibold">
                "We found the Inventory app to be a powerful tool to better organize our warehouse. It's simple and easily customizable."
              </blockquote>

              <div className="mb-8">
                <p className="text-xl font-bold text-[#0a1e3a] mb-2">Mario Riva</p>
                <p className="text-lg text-[#0a1e3a]/70">Chief Operating Officer</p>
              </div>

              {/* Enhanced Navigation Buttons */}
              <div className="flex justify-center space-x-6">
                <button className="w-14 h-14 rounded-full bg-[#0a1e3a] text-white flex items-center justify-center hover:bg-[#2a5a3a] transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110">
                  <i className="fas fa-arrow-left text-lg"></i>
                </button>
                <button className="w-14 h-14 rounded-full bg-[#0a1e3a] text-white flex items-center justify-center hover:bg-[#2a5a3a] transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110">
                  <i className="fas fa-arrow-right text-lg"></i>
                </button>
              </div>
            </div>

          </div>

        </section>

        <Footer />
      </div>
    </>
  );
}