import Header from '../../components/Header';
import Footer from '../../components/Footer';

export default function FulfillmentPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="solutions" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern e-commerce fulfillment center"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-box text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Fulfillment Solutions</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              E-Commerce Fulfillment
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                Made Simple
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Scale your e-commerce operations with our flexible fulfillment network designed for modern retail demands.
            </p>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Fulfillment Features
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-clock text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Same-Day Shipping</h3>
              <p className="text-body leading-relaxed">
                Meet customer expectations with same-day and next-day delivery options across major markets.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-sync-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Real-Time Integration</h3>
              <p className="text-body leading-relaxed">
                Seamlessly integrate with your e-commerce platform for automated order processing and inventory management.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-undo text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Returns Management</h3>
              <p className="text-body leading-relaxed">
                Streamlined returns processing to improve customer satisfaction and inventory recovery.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Analytics & Reporting</h3>
              <p className="text-body leading-relaxed">
                Detailed insights into fulfillment performance, costs, and customer satisfaction metrics.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-shield-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Quality Assurance</h3>
              <p className="text-body leading-relaxed">
                Rigorous quality control processes ensure accurate picking, packing, and shipping every time.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-expand-arrows-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Scalable Operations</h3>
              <p className="text-body leading-relaxed">
                Easily scale up or down based on seasonal demands and business growth without long-term commitments.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Fulfillment Process
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">1</span>
              </div>
              <h3 className="heading-tertiary">Order Received</h3>
              <p className="text-body leading-relaxed">
                Orders automatically flow from your e-commerce platform to our fulfillment centers.
              </p>
            </div>

            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">2</span>
              </div>
              <h3 className="heading-tertiary">Pick & Pack</h3>
              <p className="text-body leading-relaxed">
                Our trained staff carefully pick and pack your products with quality control checks.
              </p>
            </div>

            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">3</span>
              </div>
              <h3 className="heading-tertiary">Ship & Track</h3>
              <p className="text-body leading-relaxed">
                Orders are shipped with tracking information automatically sent to customers.
              </p>
            </div>

            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">4</span>
              </div>
              <h3 className="heading-tertiary">Delivered</h3>
              <p className="text-body leading-relaxed">
                Products reach customers quickly with delivery confirmation and satisfaction tracking.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="heading-secondary mb-6">
            Ready to Scale Your Fulfillment?
          </h2>
          <p className="text-body-large mb-8 max-w-2xl mx-auto">
            Let's discuss how our fulfillment solutions can help you deliver exceptional customer experiences.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="/contact" className="btn-primary btn-xl">
              Get Started
            </a>
            <a href="/solutions" className="btn-secondary btn-xl">
              View All Solutions
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
