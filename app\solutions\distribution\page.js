import Header from '../../components/Header';
import Footer from '../../components/Footer';

export default function DistributionPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="solutions" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern distribution center and logistics"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-truck text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Distribution Solutions</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              Flexible Distribution
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                Network Solutions
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Scale your distribution operations with our flexible network of strategically located facilities across North America.
            </p>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Section Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-star text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Key Benefits</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Distribution
              <span className="block text-[#4caf50]">Benefits</span>
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-shipping-fast text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Faster Delivery</h3>
              <p className="text-body leading-relaxed">
                Reduce shipping times and costs with strategically positioned distribution centers closer to your customers.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-expand-arrows-alt text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Scalable Network</h3>
              <p className="text-body leading-relaxed">
                Expand or contract your distribution footprint based on seasonal demands and business growth.
              </p>
            </div>

            <div className="card-professional card-elevated">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-dollar-sign text-black text-2xl"></i>
              </div>
              <h3 className="heading-tertiary">Cost Optimization</h3>
              <p className="text-body leading-relaxed">
                Reduce transportation costs and improve margins with optimized distribution strategies.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              How Distribution Works
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">1</span>
              </div>
              <h3 className="heading-tertiary">Network Analysis</h3>
              <p className="text-body leading-relaxed">
                We analyze your current distribution network and identify optimization opportunities.
              </p>
            </div>

            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">2</span>
              </div>
              <h3 className="heading-tertiary">Strategic Placement</h3>
              <p className="text-body leading-relaxed">
                Deploy inventory across our network of distribution centers for optimal coverage.
              </p>
            </div>

            <div className="card-professional card-elevated text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-black">3</span>
              </div>
              <h3 className="heading-tertiary">Continuous Optimization</h3>
              <p className="text-body leading-relaxed">
                Monitor performance and adjust distribution strategy based on real-time data.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="card-professional card-elevated p-16">
            <h2 className="heading-secondary mb-6">
              Ready to Optimize Your
              <span className="block text-success">Distribution?</span>
            </h2>
            <p className="text-body-large mb-8 max-w-3xl mx-auto">
              Let's discuss how our flexible distribution network can improve your delivery times and reduce costs.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <a href="/contact" className="btn-primary btn-xl">
                Get Started Today
              </a>
              <a href="/solutions" className="btn-secondary btn-xl">
                View All Solutions
              </a>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
